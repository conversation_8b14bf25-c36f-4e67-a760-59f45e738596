# Assessment Status Management APIs

## Overview
These APIs allow users to update the status of their assessments. The system ensures that only the assessment owner can modify the status and validates the current state before allowing transitions.

## Assessment Status Values
Based on `AssessmentConstants.ts`:
- `CREATED` (1): Assessment has been created but not started
- `IN_PROGRESS` (2): Assessment has been started by the user
- `COMPLETED` (0): Assessment has been completed by the user
- `LEFT` (4): Assessment was abandoned by the user
- `TIMED_OUT` (5): Assessment timed out
- `RESULT_DECLARED` (6): Assessment results have been declared

---

## 1. Start Assessment Status API

### Endpoint
`POST /api/assessment/start-assessment-status`

### Description
Updates an assessment status from `CREATED` to `IN_PROGRESS` (started).

### Authentication
Requires Bearer token authentication.

### Request Format

#### Headers
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

#### Request Body
```json
{
    "assessment_id": 123
}
```

### Parameters
- `assessment_id` (number, required): The ID of the assessment to start

### Validation Rules
1. **Authentication**: Valid Bearer token required
2. **Assessment ID**: Must be a valid number
3. **Ownership**: Assessment must belong to the authenticated user
4. **Status**: Assessment must be in `CREATED` status to be started

### Response Format

#### Success Response (200)
```json
{
    "success": true,
    "data": {
        "message": "Assessment status updated to started successfully",
        "assessment_id": 123,
        "status": "IN_PROGRESS",
        "status_code": 2
    }
}
```

#### Error Responses

##### 400 Bad Request - Invalid Assessment ID
```json
{
    "success": false,
    "error": {
        "code": "invalidAssessmentId",
        "message": "Invalid assessment ID provided"
    }
}
```

##### 400 Bad Request - Invalid Status
```json
{
    "success": false,
    "error": {
        "code": "invalidAssessmentStatus",
        "message": "Assessment is not in a valid state to be started"
    }
}
```

##### 403 Forbidden - Not Authorized
```json
{
    "success": false,
    "error": {
        "code": "assessmentNotAuthorized",
        "message": "Assessment does not belong to the authenticated user"
    }
}
```

##### 404 Not Found
```json
{
    "success": false,
    "error": {
        "code": "assessmentNotFound",
        "message": "Assessment not found"
    }
}
```

---

## 2. Complete Assessment Status API

### Endpoint
`POST /api/assessment/complete-assessment-status`

### Description
Updates an assessment status from `CREATED` or `IN_PROGRESS` to `COMPLETED`.

### Authentication
Requires Bearer token authentication.

### Request Format

#### Headers
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

#### Request Body
```json
{
    "assessment_id": 123
}
```

### Parameters
- `assessment_id` (number, required): The ID of the assessment to complete

### Validation Rules
1. **Authentication**: Valid Bearer token required
2. **Assessment ID**: Must be a valid number
3. **Ownership**: Assessment must belong to the authenticated user
4. **Status**: Assessment must be in `CREATED` or `IN_PROGRESS` status to be completed

### Response Format

#### Success Response (200)
```json
{
    "success": true,
    "data": {
        "message": "Assessment status updated to completed successfully",
        "assessment_id": 123,
        "status": "COMPLETED",
        "status_code": 0
    }
}
```

#### Error Responses
Same error response format as Start Assessment API.

---

## Security Features

### User Authentication
- Both APIs require valid Bearer token authentication
- User identity is extracted from the token

### Assessment Ownership Validation
- The system verifies that the assessment belongs to the authenticated user
- Cross-user access is prevented with 403 Forbidden response

### Status Transition Validation
- **Start Assessment**: Only allows transition from `CREATED` status
- **Complete Assessment**: Allows transition from `CREATED` or `IN_PROGRESS` status
- Invalid status transitions are rejected with appropriate error messages

---

## Usage Examples

### Example 1: Start Assessment
```bash
curl -X POST "{{base_url}}/api/assessment/start-assessment-status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": 123
  }'
```

### Example 2: Complete Assessment
```bash
curl -X POST "{{base_url}}/api/assessment/complete-assessment-status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": 123
  }'
```

---

## Typical Workflow

1. **Assessment Creation**: Assessment is created with `CREATED` status
2. **Start Assessment**: User calls start-assessment API to change status to `IN_PROGRESS`
3. **Answer Questions**: User submits answers using the save-assessment-response API
4. **Complete Assessment**: User calls complete-assessment API to change status to `COMPLETED`
5. **Result Processing**: System processes results and updates status to `RESULT_DECLARED`

---

## Error Handling

All APIs follow consistent error response format:
- `success`: false for errors
- `error.code`: Machine-readable error code
- `error.message`: Human-readable error message

Common error scenarios:
- Missing or invalid authentication token
- Assessment not found
- Assessment doesn't belong to user
- Invalid status transition
- Database connection issues

---

## Database Impact

Both APIs update the `user_assessments` table:
- Updates the `status` field
- Updates the `updated_at` timestamp
- Maintains audit trail of status changes
