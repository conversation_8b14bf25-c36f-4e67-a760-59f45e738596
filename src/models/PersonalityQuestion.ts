import sequelize, {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions, Op, Sequelize
} from 'sequelize';
import { PersonalityQuestionModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

class PersonalityQuestionModel extends Model {
}

export default class PersonalityQuestion {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        PersonalityQuestionModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'PersonalityQuestion',
                tableName: 'personality_questions',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(personalityQuestion: PersonalityQuestionModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`PersonalityQuestion-create::Creating with: ${JSON.stringify(personalityQuestion)}`);
            savedResult = await PersonalityQuestionModel.create(personalityQuestion);
            Logger.debug(`PersonalityQuestion-create::Saved personality questions: ${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`PersonalityQuestion-create::Error saving user record. Exception ${JSON.stringify(e)}`);
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async get50QuestionGroupByProfession(): Promise<PersonalityQuestionModelAttributes[] | any[]> {
        try {
            const questionByType = await PersonalityQuestionModel.findAll({
                attributes: ["id", "text", "profession_type"],
                where: {
                    id: {
                        [Op.in]: Sequelize.literal(`(
                            SELECT id 
                            FROM (
                                SELECT id, ROW_NUMBER() OVER (PARTITION BY profession_type ORDER BY RANDOM()) as rn
                                FROM "personality_questions"
                            ) AS subquery
                            WHERE rn <= 5)`
                        ),
                    },
                },
            });
            Logger.debug(`PersonalityQuestion-get50QuestionGroupByProfession: Question list with each type: ${JSON.stringify(questionByType)}`)
            return questionByType;
        } catch (e: any) {
            Logger.info(`PersonalityQuestion-get50QuestionGroupByProfession: Error getting questions. Exception: ${JSON.stringify(e.message)}`)
            return [];
        }
    }

    public async findByPk(id: number): Promise<PersonalityQuestionModelAttributes | null> {
        try {
            const result = await PersonalityQuestionModel.findByPk(id);
            if (!result) {
                return null;
            }
            return this.convertToModelAttributes(result);
        } catch (e: any) {
            Logger.error(`PersonalityQuestion::findByPk::Error finding record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    private convertToModelAttributes(record: any): PersonalityQuestionModelAttributes {
        return {
            id: record.id,
            text: record.text,
            question: record.question,
            professionId: record.professionId,
            professionType: record.professionType,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt
        };
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            text: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            professionType: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            interestTags: {
                type: DataTypes.ARRAY(DataTypes.INTEGER),
                allowNull: false,
                defaultValue: [],
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["profession_type"]
            },
            {
                unique: false,
                fields: ["interest_tags"]
            }
        ];
    }
}

