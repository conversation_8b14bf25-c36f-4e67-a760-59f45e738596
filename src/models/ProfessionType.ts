import sequelize, {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Sequelize,
    WhereOptions
} from 'sequelize';
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

interface ProfessionTypeModelAttributes {
    id: number;
    name: string;
    createdAt: Date;
    updatedAt: Date;
}

class ProfessionTypeModel extends Model<ProfessionTypeModelAttributes> implements ProfessionTypeModelAttributes {
    public id!: number;
    public name!: string;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class ProfessionType {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        ProfessionTypeModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'ProfessionType',
                tableName: 'profession_types',
                timestamps: true
            },
        );
    }

    public async findAll(options: any): Promise<ProfessionTypeModelAttributes[]> {
        try {
            const results = await ProfessionTypeModel.findAll(options);
            return results.map(result => result.get({ plain: true }));
        } catch (e: any) {
            Logger.error(`ProfessionType-findAll: Error finding records. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async getById(id: number): Promise<ProfessionTypeModelAttributes | null> {
        try {
            Logger.debug(`ProfessionType-getById: Finding profession type with id: ${id}`);

            const professionType = await ProfessionTypeModel.findByPk(id, { raw: true });

            if (professionType) {
                Logger.info(`ProfessionType-getById: Successfully retrieved profession type with id: ${id}`);
            } else {
                Logger.warn(`ProfessionType-getById: No profession type found with id: ${id}`);
            }

            return professionType;
        } catch (error) {
            Logger.error(`ProfessionType-getById: Error getting profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getByIds(ids: number[]): Promise<ProfessionTypeModelAttributes[]> {
        try {
            Logger.debug(`ProfessionType-getByIds: Finding profession types with ids: ${JSON.stringify(ids)}`);

            const queryOptions = {
                where: {
                    id: {
                        [sequelize.Op.in]: ids
                    }
                },
                raw: true
            };

            const professionTypes = await ProfessionTypeModel.findAll(queryOptions);
            Logger.debug(`ProfessionType-getByIds: Found ${professionTypes.length} profession types`);

            if (professionTypes.length > 0) {
                Logger.info(`ProfessionType-getByIds: Successfully retrieved profession types`);
            } else {
                Logger.warn(`ProfessionType-getByIds: No profession types found for the given ids`);
            }

            return professionTypes;
        } catch (error) {
            Logger.error(`ProfessionType-getByIds: Error getting profession types. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async create(professionType: ProfessionTypeModelAttributes): Promise<ProfessionTypeModelAttributes> {
        try {
            Logger.debug(`ProfessionType-create: Creating new profession type: ${JSON.stringify(professionType)}`);

            const newProfessionType = await ProfessionTypeModel.create(professionType);
            Logger.info(`ProfessionType-create: Successfully created profession type with id: ${newProfessionType.id}`);

            return newProfessionType.get({ plain: true });
        } catch (error) {
            Logger.error(`ProfessionType-create: Error creating profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async update(id: number, values: Partial<ProfessionTypeModelAttributes>): Promise<boolean> {
        try {
            Logger.debug(`ProfessionType-update: Updating profession type with id ${id}: ${JSON.stringify(values)}`);

            const [updatedRows] = await ProfessionTypeModel.update(values, {
                where: { id }
            });

            if (updatedRows > 0) {
                Logger.info(`ProfessionType-update: Successfully updated profession type with id: ${id}`);
                return true;
            } else {
                Logger.warn(`ProfessionType-update: No profession type found with id: ${id}`);
                return false;
            }
        } catch (error) {
            Logger.error(`ProfessionType-update: Error updating profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getProfessionTypeNames(professionTypeIds: number[]): Promise<Record<number, string>> {
        try {
            Logger.debug(`ProfessionType-getProfessionTypeNames: Getting names for profession types: ${JSON.stringify(professionTypeIds)}`);

            const professionTypes = await ProfessionTypeModel.findAll({
                attributes: ['id', 'name'],
                where: {
                    id: {
                        [sequelize.Op.in]: professionTypeIds
                    }
                },
                raw: true
            });

            Logger.debug(`ProfessionType-getProfessionTypeNames: Found ${professionTypes.length} profession types`);

            const result = professionTypes.reduce((acc: Record<number, string>, type: any) => {
                acc[type.id] = type.name;
                return acc;
            }, {});

            if (Object.keys(result).length > 0) {
                Logger.info(`ProfessionType-getProfessionTypeNames: Successfully retrieved profession type names`);
            } else {
                Logger.warn(`ProfessionType-getProfessionTypeNames: No profession types found for the given ids`);
            }

            return result;
        } catch (error) {
            Logger.error(`ProfessionType-getProfessionTypeNames: Error getting profession type names. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                field: 'updated_at'
            },
        }
    }
}
