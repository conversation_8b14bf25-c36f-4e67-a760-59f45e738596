import sequelize, {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions, Op, Sequelize
} from 'sequelize';
import { OccupationAssessmentSetModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";


class OccupationAssessmentSetModel extends Model<OccupationAssessmentSetModelAttributes> implements OccupationAssessmentSetModelAttributes {
    public id!: number;
    public assessmentId!: number;
    public occupationOption1!: number;
    public occupationOption2!: number;
    public userId!: number;
    public userChoice!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
}


export default class OccupationAssessmentSet {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        OccupationAssessmentSetModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'OccupationAssessmentSet',
                tableName: 'occupation_assessment_set',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(occupationAssessmentSet: OccupationAssessmentSetModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`OccupationAssessmentSet-create::Creating with: ${JSON.stringify(occupationAssessmentSet)}`);
            savedResult = await OccupationAssessmentSetModel.create(occupationAssessmentSet);
            Logger.debug(`OccupationAssessmentSet-create::Saved occupation questions: ${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`OccupationAssessmentSet-create::Error saving user record. Exception ${JSON.stringify(e)}`);
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async bulkCreate(occupationAssessmentSets: OccupationAssessmentSetModelAttributes[]): Promise<Success | Error> {
        let savedResults;
        try {
            Logger.debug(`OccupationAssessmentSet-bulkCreate::Creating ${occupationAssessmentSets.length} assessment sets`);
            savedResults = await OccupationAssessmentSetModel.bulkCreate(occupationAssessmentSets);
            Logger.debug(`OccupationAssessmentSet-bulkCreate::Saved ${savedResults.length} occupation questions`);
            return {
                success: true,
                data: savedResults
            };
        } catch (e: any) {
            Logger.error(`OccupationAssessmentSet-bulkCreate::Error saving assessment sets. Exception ${JSON.stringify(e.message)}`);
            return {
                success: false,
                errorData: JSON.stringify(e.message)
            };
        }
    }

    public async getByAssessmentId(assessmentId: number): Promise<OccupationAssessmentSetModelAttributes[] | null> {
        try {
            const assessmentSet = await OccupationAssessmentSetModel.findAll({
                where: {
                    assessmentId: assessmentId
                },
                raw: true
            });
            Logger.debug(`OccupationAssessmentSet-getByAssessmentId: Found assessment set: ${JSON.stringify(assessmentSet)}`);
            return assessmentSet;
        } catch (e: any) {
            Logger.error(`OccupationAssessmentSet-getByAssessmentId: Error getting assessment set. Exception: ${JSON.stringify(e)}`);
            return null;
        }
    }

    public async get50QuestionGroupByProfession(): Promise<OccupationAssessmentSetModelAttributes | any | null> {
        try {
            const questionByType = await OccupationAssessmentSetModel.findAll({
                attributes: ["id", "question", "type"],
                where: {
                    id: {
                        [Op.in]: Sequelize.literal(`(
                            SELECT id 
                            FROM (
                                SELECT id, ROW_NUMBER() OVER (PARTITION BY type ORDER BY RANDOM()) as rn
                                FROM "Questions"
                            ) AS subquery
                            WHERE rn <= 5)`
                        ),
                    },
                },
            });
            Logger.debug(`OccupationAssessmentSet-get50QuestionGroupByProfession: Question list with each type: ${JSON.stringify(questionByType)}`)
            return questionByType;
        } catch (e: any) {
            Logger.info(`OccupationAssessmentSet-get50QuestionGroupByProfession: Error getting questions. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    private getSchema() {
        return {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            assessmentId: {
                type: sequelize.INTEGER,
                allowNull: false
            },
            occupationOption1: {
                type: sequelize.INTEGER,
                allowNull: false
            },
            occupationOption2: {
                type: sequelize.INTEGER,
                allowNull: false
            },
            userId: {
                type: sequelize.INTEGER,
                allowNull: false
            },
            userChoice: {
                type: sequelize.SMALLINT,
                allowNull: true
            },
            createdAt: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["profession_type"]
            },
            {
                unique: false,
                fields: ["interest_tags"]
            }
        ];
    }
}

