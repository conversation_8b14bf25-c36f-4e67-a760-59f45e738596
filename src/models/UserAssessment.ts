import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions, Op, Sequelize, WhereOptions
} from 'sequelize';
import { UserAssessmentModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";
import AssessmentConstants from "../constant/AssessmentConstants";
import User from "./User";

class UserAssessmentModel extends Model<UserAssessmentModelAttributes> implements UserAssessmentModelAttributes {
    public id!: number;
    public type!: number;
    public userId!: number;
    public status!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class UserAssessment {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        UserAssessmentModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'UserAssessment',
                tableName: 'user_assessments',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(userAssessment: UserAssessmentModelAttributes): Promise<UserAssessmentModelAttributes | undefined> {
        try {
            Logger.debug(`UserAssessment-create: Creating with: ${JSON.stringify(userAssessment)}`);
            const savedResult = await UserAssessmentModel.create(userAssessment);
            Logger.debug(`UserAssessment-create: Saved results: ${JSON.stringify(savedResult)}`);
            return savedResult.get({ plain: true }) as UserAssessmentModelAttributes
        } catch (e: any) {
            Logger.error(`UserAssessment-create: Error saving user record. Exception ${JSON.stringify(e)}`);
        }
    }

    public async getPendingAssessment(): Promise<UserAssessmentModelAttributes | any | null> {
        try {
            const questionByType = await UserAssessmentModel.findOne({
                where: {
                    status: {
                        [Op.in]: [
                            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted],
                            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
                        ]
                    },
                    type: AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.personalityAssessmentType]
                },
                order: [['created_at', 'desc']]
            });
            Logger.debug(`UserAssessment-getPendingAssessment: Question list with each type: ${JSON.stringify(questionByType)}`)
            return questionByType ? questionByType.get({ plain: true }) : null;
        } catch (e: any) {
            Logger.info(`UserAssessment-getPendingAssessment: Error getting questions. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async getPendingAssessmentByUserIdAndType(userId: number, type: number = AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.personalityAssessmentType]): Promise<UserAssessmentModelAttributes | any | null> {
        try {
            const userPendingAssessment = await UserAssessmentModel.findOne({
                where: {
                    userId: userId,
                    status: {
                        [Op.in]: [
                            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted],
                            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
                        ]
                    },
                    type: type
                },
                order: [['created_at', 'desc']]
            });
            Logger.debug(`UserAssessment-getPendingAssessmentByUserId: Pending assessment: ${JSON.stringify(userPendingAssessment)}`)
            return userPendingAssessment ? userPendingAssessment.get({ plain: true }) : null;
        } catch (e: any) {
            Logger.info(`UserAssessment-getPendingAssessmentByUserId: Error getting questions. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async getCompletedAssessment(): Promise<UserAssessmentModelAttributes[] | null> {
        try {
            const completedAssessment = await UserAssessmentModel.findAll({
                where: {
                    status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted]
                },
                order: [['created_at', 'desc']]
            });
            Logger.debug(`UserAssessment-getCompletedAssessment: Completed assessmentzes: ${JSON.stringify(completedAssessment)}`)
            return completedAssessment;
        } catch (e: any) {
            Logger.info(`UserAssessment-getCompletedAssessment: Error getting assessment. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async getLatestDeclaredAssessmentForUser(userId: number, assessmentType: number): Promise<UserAssessmentModelAttributes | null> {
        try {
            const latestAssessment = await UserAssessmentModel.findOne({
                where: {
                    userId: userId,
                    type: assessmentType,
                    status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusResultDeclared]
                },
                order: [['created_at', 'DESC']]
            });
            Logger.debug(`UserAssessment-getLatestCompletedAssessment: Latest completed assessment: ${JSON.stringify(latestAssessment)}`);
            return latestAssessment ? latestAssessment.get({ plain: true }) : null;
        } catch (e: any) {
            Logger.error(`UserAssessment-getLatestCompletedAssessment: Error getting latest completed assessment. Exception: ${JSON.stringify(e.message)}`);
            return null;
        }
    }

    public async findById(assessmentId: number): Promise<UserAssessmentModelAttributes | null> {
        try {
            const assessment = await UserAssessmentModel.findOne({
                where: { id: assessmentId }
            });
            Logger.debug(`UserAssessment-findById: Found assessment: ${JSON.stringify(assessment)}`);
            return assessment ? assessment.get({ plain: true }) : null;
        } catch (e: any) {
            Logger.error(`UserAssessment-findById: Error finding assessment by id. Exception: ${JSON.stringify(e.message)}`);
            return null;
        }
    }

    public async updateAssessmentStatus(status: number, assessmentId: number): Promise<UserAssessmentModelAttributes | any | null> {
        try {
            const questionByType = await UserAssessmentModel.update(
                { status: status },
                { where: { id: assessmentId } }
            );
            Logger.debug(`UserAssessment-updateAssessmentStatus: Question list with each type: ${JSON.stringify(questionByType)}`)
            return questionByType;
        } catch (e: any) {
            Logger.info(`UserAssessment-updateAssessmentStatus: Error getting questions. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async update(values: any, where: WhereOptions): Promise<void> {
        try {
            await UserAssessmentModel.update(values, { where });
        } catch (e: any) {
            Logger.error(`UserAssessment::update::Error updating record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            type: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            status: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [];
    }
}

