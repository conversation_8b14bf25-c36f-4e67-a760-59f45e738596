import PostgresModel from '../db/PostgresModel';
import AssessmentConstants from '../constant/AssessmentConstants';
import Logger from '../lib/Logger';

export default class AssessmentResultProcessor {
    private userAssessment: any;
    private personalityAssessmentSet: any;
    private personalityQuestion: any;
    private assessmentResult: any;

    constructor() {
        // this.initializeModels();
    }

    public async initializeModels() {
        const dbModels = await PostgresModel.getDbModels();
        this.userAssessment = dbModels.userAssessment;
        this.personalityAssessmentSet = dbModels.personalityAssessmentSet;
        this.personalityQuestion = dbModels.personalityQuestions;
        this.assessmentResult = dbModels.assessmentResult;
    }

    public async processCompletedAssessmentzes(): Promise<void> {
        const classInst = this;
        try {
            // Get all completed assessments that haven't been processed
            const completedAssessments = await classInst.userAssessment.getCompletedAssessment();
            if (!completedAssessments) {
                Logger.info('No completed assessmentzes to process');
                return;
            }

            for (const assessment of completedAssessments) {
                try {
                    Logger.info(`Processing assessment ${assessment.toJSON().id}`);
                    await classInst.processSingleAssessment(assessment.toJSON());
                } catch (error) {
                    Logger.error(`Error processing assessment ${assessment.id}: ${error}`);
                    continue;
                }
            }
        } catch (error) {
            Logger.error(`Error in processCompletedAssessmentzes: ${error}`);
        }
    }

    private async processSingleAssessment(assessment: any): Promise<void> {
        // Get all questions for this assessment
        const assessmentSet = await this.personalityAssessmentSet.findByAssessmentId(assessment.id);
        if (!assessmentSet) {
            Logger.error(`Assessment set not found for assessment ${assessment.id}`);
            return;
        }

        // Get user answers
        const assessmentSetAsJson = JSON.parse(JSON.stringify(assessmentSet));
        const professionScores: Record<number, number> = {};

        for (const answer of assessmentSetAsJson) {
            if (answer.userAnswer === 1) { // Only count positive answers
                let question = await this.personalityQuestion.findByPk(answer.questionId);
                if (question && question.professionType) {
                    professionScores[question.professionType] = (professionScores[question.professionType] || 0) + 1;
                }
            }
        }

        // Get top 3 profession types
        const topProfessions = Object.entries(professionScores)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([type]) => parseInt(type));

        // Create assessment result
        await this.assessmentResult.create({
            user_assessments_id: assessment.id,
            user_id: assessment.userId,
            results: topProfessions
        });

        // Update assessment status to result declared
        await this.userAssessment.updateAssessmentStatus(
            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusResultDeclared],
            assessment.id
        );

        Logger.info(`Successfully processed assessment ${assessment.id} with results: ${topProfessions.join(', ')}`);
    }
}


