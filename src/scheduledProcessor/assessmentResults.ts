import PostgresModel from '../db/PostgresModel';
import AssessmentConstants from '../constant/AssessmentConstants';
import Logger from '../lib/Logger';

export default class AssessmentResultProcessor {
    private userAssessment: any;
    private personalityAssessmentSet: any;
    private personalityQuestion: any;
    private assessmentResult: any;

    constructor() {
        // this.initializeModels();
    }

    public async initializeModels() {
        const dbModels = await PostgresModel.getDbModels();
        this.userAssessment = dbModels.userAssessment;
        this.personalityAssessmentSet = dbModels.personalityAssessmentSet;
        this.personalityQuestion = dbModels.personalityQuestions;
        this.assessmentResult = dbModels.assessmentResult;
    }

    public async processCompletedAssessments(): Promise<void> {
        const classInst = this;
        try {
            // Get all completed assessments that haven't been processed
            const completedAssessments = await classInst.userAssessment.getCompletedAssessment();
            if (!completedAssessments) {
                Logger.info('No completed assessments to process');
                return;
            }

            for (const assessment of completedAssessments) {
                try {
                    Logger.info(`Processing assessment ${assessment.toJSON().id}`);
                    await classInst.processSingleAssessment(assessment.toJSON());
                } catch (error) {
                    Logger.error(`Error processing assessment ${assessment.id}: ${error}`);
                    continue;
                }
            }
        } catch (error) {
            Logger.error(`Error in processCompletedassessments: ${error}`);
        }
    }

    /**
     * Process a specific assessment by ID (useful for manual processing or testing)
     * @param assessmentId - The ID of the assessment to process
     */
    public async processAssessmentById(assessmentId: number): Promise<boolean> {
        const classInst = this;
        try {
            await classInst.initializeModels();

            // Find the specific assessment
            const assessment = await classInst.userAssessment.findById(assessmentId);
            if (!assessment) {
                Logger.error(`Assessment ${assessmentId} not found`);
                return false;
            }

            // Check if assessment is completed
            const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
            if (assessment.status !== completedStatus) {
                Logger.error(`Assessment ${assessmentId} is not in completed status. Current status: ${assessment.status}`);
                return false;
            }

            // Process the assessment
            await classInst.processSingleAssessment(assessment);
            Logger.info(`Successfully processed assessment ${assessmentId}`);
            return true;
        } catch (error) {
            Logger.error(`Error processing assessment ${assessmentId}: ${error}`);
            return false;
        }
    }

    private async processSingleAssessment(assessment: any): Promise<void> {
        // Get all questions for this assessment
        const assessmentSet = await this.personalityAssessmentSet.findByAssessmentId(assessment.id);
        if (!assessmentSet) {
            Logger.error(`Assessment set not found for assessment ${assessment.id}`);
            return;
        }

        // Get user answers
        const assessmentSetAsJson = JSON.parse(JSON.stringify(assessmentSet));
        const professionScores: Record<number, number> = {};
        const professionQuestionCounts: Record<number, number> = {};

        for (const answer of assessmentSetAsJson) {
            // Skip unanswered questions
            if (answer.userAnswer === null || answer.userAnswer === undefined) {
                continue;
            }

            let question = await this.personalityQuestion.findByPk(answer.questionId);
            if (question && question.professionType) {
                const professionType = question.professionType;

                // Initialize if not exists
                if (!professionScores[professionType]) {
                    professionScores[professionType] = 0;
                    professionQuestionCounts[professionType] = 0;
                }

                // Calculate weighted score based on 5-point scale
                // -2 = Strongly disagree (0 points)
                // -1 = Disagree (1 point)
                //  0 = Neutral (2 points)
                //  1 = Agree (3 points)
                //  2 = Strongly agree (4 points)
                const weightedScore = this.calculateWeightedScore(answer.userAnswer);
                professionScores[professionType] += weightedScore;
                professionQuestionCounts[professionType]++;
            }
        }

        // Calculate average scores for each profession type
        const professionAverageScores: Record<number, number> = {};
        for (const professionType in professionScores) {
            const totalScore = professionScores[professionType];
            const questionCount = professionQuestionCounts[professionType];
            professionAverageScores[professionType] = questionCount > 0 ? totalScore / questionCount : 0;
        }

        // Get top 3 profession types based on average scores
        const topProfessions = Object.entries(professionAverageScores)
            .filter(([, score]) => score > 0) // Only include professions with positive scores
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([type]) => parseInt(type));

        // Ensure we have at least some results
        if (topProfessions.length === 0) {
            Logger.warn(`No positive profession scores found for assessment ${assessment.id}. Using fallback logic.`);
            // Fallback: Get professions with highest raw scores even if average is low
            const fallbackProfessions = Object.entries(professionScores)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 3)
                .map(([type]) => parseInt(type));
            topProfessions.push(...fallbackProfessions);
        }

        // Create assessment result with detailed scoring information
        await this.assessmentResult.create({
            user_assessments_id: assessment.id,
            user_id: assessment.userId,
            results: topProfessions.slice(0, 3) // Ensure only top 3
        });

        // Update assessment status to result declared
        await this.userAssessment.updateAssessmentStatus(
            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusResultDeclared],
            assessment.id
        );

        // Log detailed results for debugging
        const scoreDetails = Object.entries(professionAverageScores)
            .map(([type, score]) => `Type ${type}: ${score.toFixed(2)}`)
            .join(', ');

        Logger.info(`Successfully processed assessment ${assessment.id} with top 3 professions: ${topProfessions.join(', ')}`);
        Logger.debug(`Assessment ${assessment.id} detailed scores: ${scoreDetails}`);
    }

    /**
     * Calculate weighted score based on 5-point Likert scale
     * @param userAnswer - User's answer (-2 to 2)
     * @returns Weighted score (0 to 4)
     */
    private calculateWeightedScore(userAnswer: number): number {
        switch (userAnswer) {
            case -2: return 0; // Strongly disagree
            case -1: return 1; // Disagree
            case 0:  return 2; // Neutral
            case 1:  return 3; // Agree
            case 2:  return 4; // Strongly agree
            default:
                Logger.warn(`Invalid user answer: ${userAnswer}. Treating as neutral.`);
                return 2; // Default to neutral
        }
    }
}


