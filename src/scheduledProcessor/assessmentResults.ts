import PostgresModel from '../db/PostgresModel';
import AssessmentConstants from '../constant/AssessmentConstants';
import Logger from '../lib/Logger';

export default class AssessmentResultProcessor {
    private userAssessment: any;
    private personalityAssessmentSet: any;
    private personalityQuestion: any;
    private assessmentResult: any;

    constructor() {
        // this.initializeModels();
    }

    public async initializeModels() {
        const dbModels = await PostgresModel.getDbModels();
        this.userAssessment = dbModels.userAssessment;
        this.personalityAssessmentSet = dbModels.personalityAssessmentSet;
        this.personalityQuestion = dbModels.personalityQuestions;
        this.assessmentResult = dbModels.assessmentResult;
    }

    public async processCompletedAssessments(): Promise<void> {
        const classInst = this;
        try {
            // Get all completed assessments that haven't been processed
            const completedAssessments = await classInst.userAssessment.getCompletedAssessment();
            if (!completedAssessments) {
                Logger.info('No completed assessments to process');
                return;
            }

            for (const assessment of completedAssessments) {
                try {
                    Logger.info(`Processing assessment ${assessment.toJSON().id}`);
                    await classInst.processSingleAssessment(assessment.toJSON());
                } catch (error) {
                    Logger.error(`Error processing assessment ${assessment.id}: ${error}`);
                    continue;
                }
            }
        } catch (error) {
            Logger.error(`Error in processCompletedassessments: ${error}`);
        }
    }

    /**
     * Process a specific assessment by ID (useful for manual processing or testing)
     * @param assessmentId - The ID of the assessment to process
     */
    public async processAssessmentById(assessmentId: number): Promise<boolean> {
        const classInst = this;
        try {
            await classInst.initializeModels();

            // Find the specific assessment
            const assessment = await classInst.userAssessment.findById(assessmentId);
            if (!assessment) {
                Logger.error(`Assessment ${assessmentId} not found`);
                return false;
            }

            // Check if assessment is completed
            const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
            if (assessment.status !== completedStatus) {
                Logger.error(`Assessment ${assessmentId} is not in completed status. Current status: ${assessment.status}`);
                return false;
            }

            // Process the assessment
            await classInst.processSingleAssessment(assessment);
            Logger.info(`Successfully processed assessment ${assessmentId}`);
            return true;
        } catch (error) {
            Logger.error(`Error processing assessment ${assessmentId}: ${error}`);
            return false;
        }
    }

    private async processSingleAssessment(assessment: any): Promise<void> {
        // Get all questions for this assessment
        const assessmentSet = await this.personalityAssessmentSet.findByAssessmentId(assessment.id);
        if (!assessmentSet) {
            Logger.error(`Assessment set not found for assessment ${assessment.id}`);
            return;
        }

        // Get user answers
        const assessmentSetAsJson = JSON.parse(JSON.stringify(assessmentSet));
        const professionScores: Record<number, number> = {};
        const professionQuestionCounts: Record<number, number> = {};

        for (const answer of assessmentSetAsJson) {
            // Skip unanswered questions
            if (answer.userAnswer === null || answer.userAnswer === undefined) {
                continue;
            }

            let question = await this.personalityQuestion.findByPk(answer.questionId);
            if (question && question.professionType) {
                const professionType = question.professionType;

                // Initialize if not exists
                if (!professionScores[professionType]) {
                    professionScores[professionType] = 0;
                    professionQuestionCounts[professionType] = 0;
                }

                // Calculate weighted score based on 5-point scale
                // -2 = Strongly disagree (0 points)
                // -1 = Disagree (1 point)
                //  0 = Neutral (2 points)
                //  1 = Agree (3 points)
                //  2 = Strongly agree (4 points)
                const weightedScore = this.calculateWeightedScore(answer.userAnswer);
                professionScores[professionType] += weightedScore;
                professionQuestionCounts[professionType]++;
            }
        }

        // Validate that we have sufficient data to process
        if (!this.isAssessmentValid(professionQuestionCounts)) {
            Logger.error(`Assessment ${assessment.id} does not have sufficient answers for processing`);
            return;
        }

        // Calculate average scores for each profession type
        const professionAverageScores: Record<number, number> = {};
        for (const professionType in professionScores) {
            const totalScore = professionScores[professionType];
            const questionCount = professionQuestionCounts[professionType];
            professionAverageScores[professionType] = questionCount > 0 ? totalScore / questionCount : 0;
        }

        // Get top 3 profession types based on average scores
        // Use a minimum threshold of 1.5 (which corresponds to slightly above neutral)
        const minimumThreshold = 1.5;
        let topProfessions = Object.entries(professionAverageScores)
            .filter(([, score]) => score >= minimumThreshold) // Only include professions above threshold
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([type]) => parseInt(type));

        // If we don't have enough results above threshold, include the highest scoring ones
        if (topProfessions.length < 3) {
            Logger.info(`Only ${topProfessions.length} professions above threshold for assessment ${assessment.id}. Including additional highest scoring professions.`);

            const allProfessionsSorted = Object.entries(professionAverageScores)
                .filter(([type]) => !topProfessions.includes(parseInt(type))) // Exclude already selected
                .sort(([, a], [, b]) => b - a)
                .map(([type]) => parseInt(type));

            // Add remaining professions to reach 3 total
            const remainingSlots = 3 - topProfessions.length;
            topProfessions = topProfessions.concat(allProfessionsSorted.slice(0, remainingSlots));
        }

        // Final fallback: if still no results, use professions with any questions answered
        if (topProfessions.length === 0) {
            Logger.warn(`No profession scores found for assessment ${assessment.id}. Using fallback logic.`);
            topProfessions = Object.keys(professionQuestionCounts)
                .filter(type => professionQuestionCounts[parseInt(type)] > 0)
                .slice(0, 3)
                .map(type => parseInt(type));
        }

        // Create assessment result with detailed scoring information
        await this.assessmentResult.create({
            assessment_id: assessment.id,
            user_id: assessment.userId,
            result: topProfessions.slice(0, 3) // Ensure only top 3
        });

        // Update assessment status to result declared
        await this.userAssessment.updateAssessmentStatus(
            AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusResultDeclared],
            assessment.id
        );

        // Log detailed results for debugging
        const scoreDetails = Object.entries(professionAverageScores)
            .map(([type, score]) => `Type ${type}: ${score.toFixed(2)}`)
            .join(', ');

        Logger.info(`Successfully processed assessment ${assessment.id} with top 3 professions: ${topProfessions.join(', ')}`);
        Logger.debug(`Assessment ${assessment.id} detailed scores: ${scoreDetails}`);
    }

    /**
     * Calculate weighted score based on 5-point Likert scale
     * Uses a more nuanced scoring system that gives higher weight to positive responses
     * @param userAnswer - User's answer (-2 to 2)
     * @returns Weighted score (0 to 4)
     */
    private calculateWeightedScore(userAnswer: number): number {
        // Validate input
        if (typeof userAnswer !== 'number' || userAnswer < -2 || userAnswer > 2) {
            Logger.warn(`Invalid user answer: ${userAnswer}. Treating as neutral.`);
            return 2; // Default to neutral
        }

        switch (userAnswer) {
            case -2: return 0;   // Strongly disagree - no points
            case -1: return 1;   // Disagree - minimal points
            case 0:  return 2;   // Neutral - baseline points
            case 1:  return 3;   // Agree - good points
            case 2:  return 4;   // Strongly agree - maximum points
            default:
                return 2; // Fallback to neutral
        }
    }

    /**
     * Validates that an assessment has sufficient answers to generate meaningful results
     * @param professionQuestionCounts - Count of questions answered per profession
     * @returns boolean indicating if assessment is valid for processing
     */
    private isAssessmentValid(professionQuestionCounts: Record<number, number>): boolean {
        const totalAnsweredQuestions = Object.values(professionQuestionCounts).reduce((sum, count) => sum + count, 0);
        const minimumRequiredAnswers = 10; // Require at least 10 answered questions

        if (totalAnsweredQuestions < minimumRequiredAnswers) {
            Logger.warn(`Assessment has only ${totalAnsweredQuestions} answered questions, minimum required: ${minimumRequiredAnswers}`);
            return false;
        }

        return true;
    }
}


