'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('occupations', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: sequelize.STRING(100),
                allowNull: true,
            },
            slug: {
                type: sequelize.STRING(100),
                allowNull: true,
            },
            profession_type: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'profession_types' },
                    key: 'id'
                },
                allowNull: false
            },
            minimum_qualification: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                allowNull: false
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });

        await queryInterface.addIndex('occupations', {
            unique: false,
            fields: ["slug"]
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('occupations');
    }
}
