'use strict';

import { QueryInterface, DataTypes } from 'sequelize';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface: QueryInterface) => {
		await queryInterface.createTable('expert_profile', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			year_of_exp: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			profession_type: {
				type: DataTypes.ARRAY(DataTypes.INTEGER),
				allowNull: false
			},
			occupation_type: {
				type: DataTypes.ARRAY(DataTypes.INTEGER),
				allowNull: false
			},
			user_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: { tableName: 'users' },
					key: 'id'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			created_at: {
				allowNull: false,
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW
			},
			updated_at: {
				allowNull: false,
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW
			}
		});
	},

	down: async (queryInterface: QueryInterface) => {
		await queryInterface.dropTable('expert_profile');
	}
};
