import User from "../models/User";
import { InitOptions, Sequelize } from "sequelize";
import Database from "./DBConnect";
import School from "../models/School";
import PersonalityQuestion from "../models/PersonalityQuestion";
import UserAssessment from "../models/UserAssessment";
import PersonalityAssessmentSet from "../models/PersonalityAssessmentSet";
import OccupationAssessmentSet from "../models/OccupationAssessmentSet";
import AssessmentResult from "../models/AssessmentResult";
import Occupation from "../models/Occupation";
import ExpertProfile from "../models/ExpertProfile";
import ExpertEducation from "../models/ExpertEducation";
import ExpertOccupation from "../models/ExpertOccupation";
import CollegeTier from "../models/CollegeTier";
import ProfessionType from "../models/ProfessionType";
import UserOccupationChoice from "../models/UserOccupationChoice";
import ReferralCode from "../models/ReferralCode";
import OtpVerification from "../models/OtpVerification";

export default class PostgresModel {
    public sequelize: Sequelize;

    public user: User;
    public school: School;
    public personalityQuestions: PersonalityQuestion;
    public userAssessment: UserAssessment;
    public personalityAssessmentSet: PersonalityAssessmentSet;
    public occupationAssessmentSet: OccupationAssessmentSet;
    public assessmentResult: AssessmentResult;
    public occupation: Occupation;
    public expertProfile: ExpertProfile;
    public expertEducation: ExpertEducation;
    public expertOccupation: ExpertOccupation;
    public collegeTier: CollegeTier;
    public professionType: ProfessionType;
    public userOccupationChoice: UserOccupationChoice;
    public referralCode: ReferralCode;
    public otpVerification: OtpVerification;

    private constructor(initOptions: InitOptions) {
        const oThis = this;
        oThis.sequelize = initOptions.sequelize;
        oThis.user = new User(initOptions);
        oThis.school = new School(initOptions);
        oThis.personalityQuestions = new PersonalityQuestion(initOptions);
        oThis.userAssessment = new UserAssessment(initOptions);
        oThis.personalityAssessmentSet = new PersonalityAssessmentSet(initOptions);
        oThis.occupationAssessmentSet = new OccupationAssessmentSet(initOptions);
        oThis.assessmentResult = new AssessmentResult(initOptions);
        oThis.occupation = new Occupation(initOptions);
        oThis.expertProfile = new ExpertProfile(initOptions);
        oThis.expertEducation = new ExpertEducation(initOptions);
        oThis.expertOccupation = new ExpertOccupation(initOptions);
        oThis.collegeTier = new CollegeTier(initOptions);
        oThis.professionType = new ProfessionType(initOptions);
        oThis.userOccupationChoice = new UserOccupationChoice(initOptions);
        oThis.referralCode = new ReferralCode(initOptions);
        oThis.otpVerification = new OtpVerification(initOptions);
    }

    public static async getDbModels(): Promise<PostgresModel> {
        const sequelize: Sequelize = await Database.getInstance();
        const initOptions: InitOptions = {
            sequelize: sequelize,
            underscored: true,
            freezeTableName: true,
        };
        return new PostgresModel(initOptions);
    }
}
