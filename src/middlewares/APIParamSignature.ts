import { ApiParamSignatureInterface } from "../lib/types/Global";
import APINameConstant from "../constant/APINameConstant";


const APIParamSignature: Record<string, ApiParamSignatureInterface> = {
    [APINameConstant.userLogin]: {
        mandatory: [
            {
                parameter: 'password',
                validatorMethods: [{ validateNumber: null }],
            }
        ],
        optional: [
            {
                parameter: 'email',
                validatorMethods: [{ validateNumber: null }],
            },
            {
                parameter: 'username',
                validatorMethods: [{ validateNumber: null }],
            },
            {
                parameter: 'mobileNumber',
                validatorMethods: [{ validateNumber: null }],
            }
        ]
    },
    [APINameConstant.userSignup]: {
        mandatory: [
            {
                parameter: 'name',
                validatorMethods: [{ validateString: null }],
            },
            {
                parameter: 'email',
                validatorMethods: [{ validateString: null }],
            },
            {
                parameter: 'password',
                validatorMethods: [{ validateNumber: null }],
            },
            {
                parameter: 'username',
                validatorMethods: [{ validateNumber: null }],
            },
            {
                parameter: 'mobileNumber',
                validatorMethods: [{ validateNumber: null }],
            }
        ],
        optional: []
    },
    [APINameConstant.refreshToken]: {
        mandatory: [
            {
                parameter: 'refreshToken',
                validatorMethods: [{ validateString: null }],
            }
        ],
        optional: []
    },
    [APINameConstant.logout]: {
        mandatory: [],
        optional: []
    },

    [APINameConstant.updateUserProfile]: {
        mandatory: [],
        optional: [
            {
                parameter: 'name',
                validatorMethods: [{ validateString: null }],
            },
            {
                parameter: 'mobile_number',
                validatorMethods: [{ validateString: null }],
            },
            {
                parameter: 'password',
                validatorMethods: [{ validateString: null }],
            }
        ]
    },

    [APINameConstant.signupWithLink]: {
        mandatory: [
            {
                parameter: 'email',
                validatorMethods: [{ validateString: null }],
            }
        ],
        optional: [
            {
                parameter: 'referral_code',
                validatorMethods: [{ validateString: null }],
            }
        ]
    },

    [APINameConstant.verifyOtp]: {
        mandatory: [
            {
                parameter: 'email',
                validatorMethods: [{ validateString: null }],
            },
            {
                parameter: 'otp_code',
                validatorMethods: [{ validateString: null }],
            }
        ],
        optional: [
            {
                parameter: 'otp_type',
                validatorMethods: [{ validateString: null }],
            }
        ]
    },

    [APINameConstant.resendOtp]: {
        mandatory: [
            {
                parameter: 'email',
                validatorMethods: [{ validateString: null }],
            }
        ],
        optional: [
            {
                parameter: 'otp_type',
                validatorMethods: [{ validateString: null }],
            }
        ]
    },

    [APINameConstant.startPersonalityAssessment]: {
        mandatory: [],
        optional: []
    },

    [APINameConstant.startOccupationAssessment]: {
        mandatory: [],
        optional: []
    },

    [APINameConstant.savePersonalityAssessmentAnswer]: {
        mandatory: [],
        optional: []
    },

    [APINameConstant.getOccupationsByPersonality]: {
        mandatory: [],
        optional: []
    },

    [APINameConstant.saveOccupationChoice]: {
        mandatory: [
            {
                parameter: 'occupationIds',
                validatorMethods: [{ validateArray: null }],
            }
        ],
        optional: []
    }
};

export default APIParamSignature;
