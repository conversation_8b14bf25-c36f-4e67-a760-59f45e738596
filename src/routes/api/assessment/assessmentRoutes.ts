import express, { Request, Response } from 'express';
import bodyParser from "body-parser";
import { authenticateToken } from "../../../middlewares/AuthMiddleware";
import APINameConstant from "../../../constant/APINameConstant";
import { Success } from "../../../lib/types/Response";
import ResponseHelper from "../../../middlewares/ResponseHelper";
import Logger from "../../../lib/Logger";
import PersonalityAssessment from "../../../controller/assessment/PersonalityAssessment";
import OccupationAssessment from "../../../controller/assessment/OccupationAssessment";
import { Router } from "express";
import PersonalityAssessmentAnswer from "../../../controller/assessment/PersonalityAssessmentAnswer";
import OccupationListController from "../../../controller/OccupationListController";
import SaveOccupationChoiceController from "../../../controller/assessment/SaveOccupationChoiceController";

const router = express();

router.use(bodyParser.json());

router.get('/get-users', authenticateToken, (req: Request, res: Response) => {
    res.json({ message: `Hello ${(req as any).user.username}, welcome to the protected route!` });
});

router.post('/start-assessment', authenticateToken, async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.startPersonalityAssessment;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new PersonalityAssessment(requestBody).perform();
    Logger.debug(`assessmentRoutes-PersonalityAssessment: service response: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post("/save-assessment-response", authenticateToken, async (req, res) => {
    const apiName = APINameConstant.savePersonalityAssessmentAnswer;
    req.customParamsInReq.apiName = APINameConstant.savePersonalityAssessmentAnswer;
    let requestBody = req.body
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    const serviceResponse = await new PersonalityAssessmentAnswer(requestBody).perform();
    Logger.info(`${apiName}::serviceResponse::${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

// Deprecated APIs
router.post('/start-occupation-assessment', authenticateToken, async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.startOccupationAssessment;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new OccupationAssessment(requestBody).perform();
    Logger.debug(`assessmentRoutes-OccupationAssessment: service response: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.get('/occupations', authenticateToken, async (req, res) => {
    req.customParamsInReq.apiName = APINameConstant.getOccupationsByPersonality;
    const requestQuery = req.query as any;
    Object.assign(requestQuery, { customParamsInReq: req.customParamsInReq });
    const response = await new OccupationListController(requestQuery).perform();
    ResponseHelper.renderWithStatus(response, res);
});

router.post('/save-occupation-choice', authenticateToken, async (req, res) => {
    req.customParamsInReq.apiName = APINameConstant.saveOccupationChoice;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    const response = await new SaveOccupationChoiceController(requestBody).perform();
    Logger.debug(`assessmentRoutes-SaveOccupationChoice: service response: ${JSON.stringify(response)}`);
    ResponseHelper.renderWithStatus(response, res);
});

export default router;
