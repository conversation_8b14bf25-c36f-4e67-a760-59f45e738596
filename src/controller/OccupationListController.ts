import { Request, Response } from 'express';
import { Op } from 'sequelize';
import PostgresModel from '../db/PostgresModel';
import Logger from '../lib/Logger';
import { Error, Success } from '../lib/types/Response';
import BaseController from './BaseController';
import ResponseHelper from '../middlewares/ResponseHelper';
import { channel } from 'diagnostics_channel';

interface Occupation {
    id: number;
    name: string;
    professionType: number;
}

interface GroupedOccupations {
    [key: number]: {
        profession_id: number;
        profession_name: string;
        occupations: {
            occupation_id: number;
            occupation_name: string;
        }[];
    };
}

export default class OccupationListController extends BaseController {
    private dbModels: PostgresModel;
    private userId: number;
    private userDetails: any;

    constructor(params: any) {
        super(params);
        const classInst = this;
        classInst.userDetails = params.customParamsInReq.currentUser
        Logger.debug(`ExpertController-constructor: User details: ${JSON.stringify(classInst.userDetails)}`);
        this.userId = classInst.userDetails.userId;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;
        classInst.dbModels = await PostgresModel.getDbModels();

        try {
            const latestAssessmentResult = await classInst.dbModels.assessmentResult.getLatestAssessmentResult(this.userId);
            Logger.debug(`ExpertController-servicePerform: Latest assessment result (Profession Types): ${JSON.stringify(latestAssessmentResult)}`);

            if (!latestAssessmentResult) {
                return this.handleNoAssessmentResult();
            }

            const personalityTypes = latestAssessmentResult.result;
            const professionTypes = await this.getProfessionTypeNames(personalityTypes);
            const occupations = await this.getOccupationsByPersonalityTypes(personalityTypes);
            const groupedOccupations = this.groupOccupationsByPersonalityType(occupations, personalityTypes, professionTypes);

            return ResponseHelper.success({
                data: groupedOccupations,
            });
        } catch (error: any) {
            return this.handleError(error);
        }
    }

    private handleNoAssessmentResult(): Error {
        return ResponseHelper.error(['noAssessmentResult'], {
            message: 'No assessment results found for the user'
        });
    }

    private async getOccupationsByPersonalityTypes(personalityTypes: number[]): Promise<Occupation[]> {
        const classInst = this;
        try {
            return await classInst.dbModels.occupation.getByProfessionTypes(personalityTypes);
        } catch (error: any) {
            Logger.error(`ExpertController-getOccupationsByPersonalityTypes: Error getting occupations. Exception: ${JSON.stringify(error.message)}`);
            throw error;
        }
    }

    private async getProfessionTypeNames(professionTypeIds: number[]): Promise<Record<number, string>> {
        const classInst = this;
        try {
            return await classInst.dbModels.professionType.getProfessionTypeNames(professionTypeIds);
        } catch (error: any) {
            Logger.error(`ExpertController-getProfessionTypeNames: Error getting profession types. Exception: ${JSON.stringify(error.message)}`);
            throw error;
        }
    }

    private groupOccupationsByPersonalityType(
        occupations: Occupation[],
        personalityTypes: number[],
        professionTypes: Record<number, string>
    ): GroupedOccupations {
        return personalityTypes.reduce((acc: GroupedOccupations, type: number) => {
            acc[type] = {
                profession_id: type,
                profession_name: professionTypes[type] || 'Unknown',
                occupations: occupations
                    .filter((occ: Occupation) => occ.professionType === type)
                    .map((occ: Occupation) => ({
                        occupation_id: occ.id,
                        occupation_name: occ.name
                    }))
            };
            return acc;
        }, {});
    }

    private handleError(error: any): Error {
        Logger.error(`ExpertController-servicePerform: Error getting occupations. Exception: ${JSON.stringify(error.message)}`);
        return ResponseHelper.error(['generalError'], {
            error: error.message
        });
    }
}
