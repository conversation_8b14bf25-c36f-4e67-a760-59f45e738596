import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import GenericOpHelpler from "../../lib/utils/GenericOpHelpler";
import Logger from "../../lib/Logger";
import UserAssessment from "../../models/UserAssessment";
import OccupationAssessmentSet from "../../models/OccupationAssessmentSet";
import AssessmentResult from "../../models/AssessmentResult";
import AssessmentConstants from "../../constant/AssessmentConstants";
import Database from "../../db/DBConnect";
import { Op } from "sequelize";
import { OccupationAssessmentSetModelAttributes } from "../../lib/types/Model";
import Occupation from "../../models/Occupation";

export default class OccupationAssessment extends BaseController {
    private name: string;
    private password: string;
    private email: string;
    private username: string;
    private token: string;
    private userAssessment: UserAssessment;
    private occupationAssessmentSet: OccupationAssessmentSet;
    private assessmentResult: AssessmentResult;
    private pendingAssessment: any;
    private assessmentQuestions: any;
    private userDetails: any;
    private occupation: Occupation;

    constructor(params: any) {
        super(params);

        const classInst = this;

        classInst.userDetails = params.customParamsInReq.currentUser
        Logger.info(`OccupationAssessment: User objects: ${JSON.stringify(classInst.userDetails)}`)
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;


        const postgresModel = (await PostgresModel.getDbModels())
        classInst.userAssessment = postgresModel.userAssessment;
        classInst.occupationAssessmentSet = postgresModel.occupationAssessmentSet;
        classInst.assessmentResult = postgresModel.assessmentResult
        classInst.occupation = postgresModel.occupation;

        /**
         * Get pending assessment type occupation
         * Get question from their itself
         * Else
         * Get result of personality for user (created_at desc)
         * Create occupation assessment set
         * Return questions. 
         */
        await classInst.validateParams();
        await classInst.checkPendingAssessment();
        return classInst.prepareResponse();
    }

    private async validateParams() {
        // Verify username is unique
        // Verify email is unique
        // Verify name is not empty
        // Password strength
    }

    private async checkPendingAssessment() {
        const classInst = this;
        try {
            // Check for pending occupation assessmentzes (status 1 or 2)
            classInst.pendingAssessment = await classInst.userAssessment.getPendingAssessmentByUserIdAndType(
                classInst.userDetails.userId, AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.occupationAssessmentType]);
            if (classInst.pendingAssessment) {
                // If there's a pending assessment, get its questions
                await classInst.getPendingAssessmentQuestions();
            } else {
                // If no pending assessment, create a new one
                await classInst.createUserAssessment();
            }
        } catch (error) {
            Logger.error(`OccupationAssessment-checkPendingAssessment: Error checking pending assessment. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getPendingAssessmentQuestions() {
        const classInst = this; 
        try {
            // Get questions for the pending assessment
            const assessmentSet = await classInst.occupationAssessmentSet.getByAssessmentId(classInst.pendingAssessment.id);
            if (!assessmentSet) {
                throw new Error('Assessment set not found for pending assessment');
            }
            
            // Filter to only include required fields
            classInst.assessmentQuestions = assessmentSet.map(question => ({
                id: question.id,
                assessmentId: question.assessmentId,
                occupationOption1: question.occupationOption1,
                occupationOption2: question.occupationOption2
            }));
            
            Logger.debug(`OccupationAssessment-getPendingAssessmentQuestions: Filtered assessment questions: ${JSON.stringify(classInst.assessmentQuestions)}`);
        } catch (error) {
            Logger.error(`OccupationAssessment-getPendingAssessmentQuestions: Error getting pending assessment questions. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async createUserAssessment() {
        const classInst = this;
        try {
            // Get latest personality assessment result
            const personalityAssessmentResult = await classInst.getLatestPersonalityAssessmentResult();
            Logger.debug(`OccupationAssessment-createUserAssessment: Personality assessment result: ${JSON.stringify(personalityAssessmentResult)}`);
            if (!personalityAssessmentResult) {
                throw new Error('No personality assessment result found');
            }

            // Create new occupation assessment with status 1 (Created)
            const newAssessment = await classInst.userAssessment.create({
                type: AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.occupationAssessmentType],
                userId: classInst.userDetails.userId,
                status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
            });

            if (!newAssessment || !newAssessment.id) {
                throw new Error('OccupationAssessment-createUserAssessment: Failed to create new assessment');
            }
            classInst.pendingAssessment = newAssessment

            // Generate occupation questions based on personality types
            await classInst.generateOccupationQuestions(newAssessment.id, personalityAssessmentResult.results);
        } catch (error) {
            Logger.error(`OccupationAssessment-createUserAssessment: Error creating new assessment. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getLatestPersonalityAssessmentResult(): Promise<any> {
        const classInst = this;
        try {
            // Get the latest completed personality assessment
            const latestAssessment = await classInst.userAssessment.getLatestDeclaredAssessmentForUser(
                classInst.userDetails.userId,
                AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.personalityAssessmentType]
            );

            if (!latestAssessment) {
                return null;
            }

            // Get the assessment result
            return await classInst.assessmentResult.getByUserAssessmentId(latestAssessment.id!);
        } catch (error) {
            Logger.error(`OccupationAssessment-getLatestPersonalityAssessmentResult: Error getting latest assessment result. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async generateOccupationQuestions(assessmentId: number, personalityTypes: number[]) {
        const classInst = this;
        try {
            Logger.debug(`OccupationAssessment-generateOccupationQuestions: Generating questions for assessment ${assessmentId} with personality types: ${JSON.stringify(personalityTypes)}`);
            
            // Get 10 random occupation pairs
            const occupationPairs = await classInst.getOccupationPairs(personalityTypes);
            Logger.debug(`OccupationAssessment-generateOccupationQuestions: Generated ${occupationPairs.length} occupation pairs`);
            
            // Prepare assessment sets for bulk create
            const assessmentSets: OccupationAssessmentSetModelAttributes[] = occupationPairs.map(pair => ({
                id: undefined,
                assessmentId: assessmentId,
                userId: classInst.userDetails.userId,
                occupationOption1: pair.option1,
                occupationOption2: pair.option2,
                userChoice: 0 // Initialize with no choice
            }));

            Logger.debug(`OccupationAssessment-generateOccupationQuestions: Prepared ${assessmentSets.length} assessment sets for bulk create`);
            
            // Bulk create assessment sets
            const result = await classInst.occupationAssessmentSet.bulkCreate(assessmentSets);
            if (!result.success) {
                throw new Error('Failed to create assessment sets');
            }
            
            Logger.info(`OccupationAssessment-generateOccupationQuestions: Successfully created ${assessmentSets.length} assessment sets`);

            // Set assessmentQuestions with only required fields
            classInst.assessmentQuestions = assessmentSets.map(question => ({
                id: question.id,
                assessmentId: question.assessmentId,
                occupationOption1: question.occupationOption1,
                occupationOption2: question.occupationOption2
            }));
            
            Logger.debug(`OccupationAssessment-generateOccupationQuestions: Set assessment questions: ${JSON.stringify(classInst.assessmentQuestions)}`);
        } catch (error) {
            Logger.error(`OccupationAssessment-generateOccupationQuestions: Error generating questions. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getOccupationPairs(personalityTypes: number[]): Promise<any[]> {
        const classInst = this;
        try {
            const pairs = [];
            const usedOccupations = new Set<number>();
            
            // Get 10 pairs of occupations
            for (let i = 0; i < 10; i++) {
                // Get two random occupations from different personality types
                const pair = await classInst.getRandomOccupationPair(personalityTypes, usedOccupations);
                if (pair) {
                    pairs.push(pair);
                    usedOccupations.add(pair.option1);
                    usedOccupations.add(pair.option2);
                }
            }
            Logger.info(`OccupationAssessment-getOccupationPairs: Generated occupation pairs: ${JSON.stringify(pairs)}`);
            return pairs;
        } catch (error) {
            Logger.error(`OccupationAssessment-getOccupationPairs: Error getting occupation pairs. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getRandomOccupationPair(personalityTypes: number[], usedOccupations: Set<number>): Promise<any> {
        const classInst = this;
        try {
            Logger.debug(`OccupationAssessment-getRandomOccupationPair: Getting random occupations for personality types: ${JSON.stringify(personalityTypes)}`);
            Logger.debug(`OccupationAssessment-getRandomOccupationPair: Already used occupations: ${JSON.stringify(Array.from(usedOccupations))}`);

            // Filter out used profession types
            const availableTypes = personalityTypes.filter(type => !usedOccupations.has(type));
            Logger.debug(`OccupationAssessment-getRandomOccupationPair: Available profession types: ${JSON.stringify(availableTypes)}`);

            // Get two random occupations from different personality types
            let occupations = await classInst.occupation.getRandomOccupationsByProfessionTypes(availableTypes);
            Logger.debug(`OccupationAssessment-getRandomOccupationPair: Found occupations: ${JSON.stringify(occupations)}`);
            if (!occupations || occupations.length < 2) {
                Logger.warn('OccupationAssessment-getRandomOccupationPair: Not enough unique occupations found');
                return null;
            }

            const result = {
                option1: occupations[0].id,
                option2: occupations[1].id
            };
            Logger.info(`OccupationAssessment-getRandomOccupationPair: Selected occupation pair: ${JSON.stringify(result)}`);
            return result;
        } catch (error: any) {
            Logger.error(`OccupationAssessment-getRandomOccupationPair: Error getting random occupation pair. Exception: ${JSON.stringify(error.message)}`);
            throw error;
        }
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            data: {
                assessment: classInst.pendingAssessment,
                questions: classInst.assessmentQuestions
            }
        });
    }
}
