import BaseController from "../BaseController";
import { Error, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface RequestBody {
    assessment_id: number;
    customParamsInReq: any;
}

export default class CompleteAssessmentController extends BaseController {
    private assessmentId: number;
    private userId: number;
    private userAssessment: any;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`CompleteAssessmentController-constructor: Param body: ${JSON.stringify(params)}`);
        
        classInst.assessmentId = params.assessment_id;
        classInst.userId = params.customParamsInReq?.currentUser?.id;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.validateAssessmentOwnership();
        await classInst.updateAssessmentStatus();

        return classInst.prepareResponse();
    }

    private async validateParams() {
        const classInst = this;

        // Validate assessment_id
        if (!classInst.assessmentId || typeof classInst.assessmentId !== 'number') {
            return classInst.unauthorizedResponse("invalidAssessmentId", "400");
        }

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }
    }

    private async validateAssessmentOwnership() {
        const classInst = this;
        const userAssessmentModel = (await PostgresModel.getDbModels()).userAssessment;

        try {
            // Find the assessment by ID and verify it belongs to the current user
            const assessment = await userAssessmentModel.findByPk(classInst.assessmentId);
            
            if (!assessment) {
                Logger.error(`CompleteAssessmentController-validateAssessmentOwnership: Assessment not found for id ${classInst.assessmentId}`);
                return classInst.unauthorizedResponse("assessmentNotFound", "404");
            }

            const assessmentData = assessment.get({ plain: true });
            
            if (assessmentData.userId !== classInst.userId) {
                Logger.error(`CompleteAssessmentController-validateAssessmentOwnership: Assessment ${classInst.assessmentId} does not belong to user ${classInst.userId}`);
                return classInst.unauthorizedResponse("assessmentNotAuthorized", "403");
            }

            // Check if assessment is in a valid state to be completed
            const currentStatus = assessmentData.status;
            const startedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];
            const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];

            if (currentStatus !== startedStatus && currentStatus !== completedStatus) {
                Logger.error(`CompleteAssessmentController-validateAssessmentOwnership: Assessment ${classInst.assessmentId} is in invalid state ${currentStatus} to be completed`);
                return classInst.unauthorizedResponse("invalidAssessmentState", "400");
            }

            classInst.userAssessment = assessmentData;
            Logger.debug(`CompleteAssessmentController-validateAssessmentOwnership: Assessment validation successful for id ${classInst.assessmentId}`);
        } catch (error) {
            Logger.error(`CompleteAssessmentController-validateAssessmentOwnership: Error validating assessment. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("validationFailed", "500");
        }
    }

    private async updateAssessmentStatus() {
        const classInst = this;
        const userAssessmentModel = (await PostgresModel.getDbModels()).userAssessment;

        try {
            const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
            
            await userAssessmentModel.updateAssessmentStatus(completedStatus, classInst.assessmentId);
            
            Logger.debug(`CompleteAssessmentController-updateAssessmentStatus: Assessment ${classInst.assessmentId} status updated to COMPLETED (${completedStatus})`);
        } catch (error) {
            Logger.error(`CompleteAssessmentController-updateAssessmentStatus: Error updating assessment status. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
        
        return ResponseHelper.success({
            message: "Assessment completed successfully",
            assessment_id: classInst.assessmentId,
            status: completedStatus,
            status_description: AssessmentConstants.assessmentStatusCompleted,
            user_id: classInst.userId,
            assessment_type: classInst.userAssessment.type,
            completed_at: new Date().toISOString()
        });
    }
}
