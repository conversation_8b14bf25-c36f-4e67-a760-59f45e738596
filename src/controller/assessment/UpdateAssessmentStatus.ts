import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import { UserAssessmentModelAttributes } from "../../lib/types/Model";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface RequestBody {
    assessment_id: number;
    status: string; // "started" or "completed"
    customParamsInReq: any;
}

export default class UpdateAssessmentStatus extends BaseController {
    private assessmentId: number;
    private requestedStatus: string;
    private userId: number;
    private userAssessment: any;
    private targetStatusCode: number;
    private targetStatusName: string;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`UpdateAssessmentStatus-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`UpdateAssessmentStatus-constructor: User details: ${JSON.stringify(params.customParamsInReq?.currentUser)}`)
        
        classInst.assessmentId = params.assessment_id;
        classInst.requestedStatus = params.status;
        classInst.userId = params.customParamsInReq?.currentUser?.id;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.initializeModels();
        await classInst.validateParams();
        await classInst.validateAssessmentOwnership();
        await classInst.updateAssessmentStatus();

        return classInst.prepareResponse();
    }

    private async initializeModels() {
        const classInst = this;
        const postgresModel = await PostgresModel.getDbModels();
        classInst.userAssessment = postgresModel.userAssessment;
    }

    private async validateParams() {
        const classInst = this;

        // Validate assessment_id
        if (!classInst.assessmentId || typeof classInst.assessmentId !== 'number') {
            return classInst.unauthorizedResponse("invalidAssessmentId", "400");
        }

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }

        // Validate and set target status
        if (!classInst.requestedStatus) {
            return classInst.unauthorizedResponse("missingStatus", "400");
        }

        switch (classInst.requestedStatus.toLowerCase()) {
            case "started":
            case "start":
                classInst.targetStatusCode = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];
                classInst.targetStatusName = AssessmentConstants.assessmentStatusStarted;
                break;
            case "completed":
            case "complete":
                classInst.targetStatusCode = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
                classInst.targetStatusName = AssessmentConstants.assessmentStatusCompleted;
                break;
            default:
                return classInst.unauthorizedResponse("invalidStatus", "400");
        }
    }

    private async validateAssessmentOwnership() {
        const classInst = this;

        try {
            // Find the assessment by ID
            const assessment = await classInst.userAssessment.findById(classInst.assessmentId);
            
            if (!assessment) {
                Logger.error(`UpdateAssessmentStatus-validateAssessmentOwnership: Assessment not found for id ${classInst.assessmentId}`);
                return classInst.unauthorizedResponse("assessmentNotFound", "404");
            }

            // Check if the assessment belongs to the authenticated user
            if (assessment.userId !== classInst.userId) {
                Logger.error(`UpdateAssessmentStatus-validateAssessmentOwnership: Assessment ${classInst.assessmentId} does not belong to user ${classInst.userId}`);
                return classInst.unauthorizedResponse("assessmentNotAuthorized", "403");
            }

            // Validate status transition based on current status and requested status
            const currentStatus = assessment.status;
            const isValidTransition = classInst.validateStatusTransition(currentStatus, classInst.targetStatusCode);

            if (!isValidTransition) {
                Logger.error(`UpdateAssessmentStatus-validateAssessmentOwnership: Invalid status transition from ${currentStatus} to ${classInst.targetStatusCode} for assessment ${classInst.assessmentId}`);
                return classInst.unauthorizedResponse("invalidStatusTransition", "400");
            }

        } catch (error) {
            Logger.error(`UpdateAssessmentStatus-validateAssessmentOwnership: Error validating assessment ownership. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("validationFailed", "500");
        }
    }

    private validateStatusTransition(currentStatus: number, targetStatus: number): boolean {
        const createdStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated];
        const startedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];
        const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];

        // Define valid transitions
        const validTransitions: Record<number, number[]> = {
            [createdStatus]: [startedStatus, completedStatus], // CREATED can go to STARTED or COMPLETED
            [startedStatus]: [completedStatus], // STARTED can go to COMPLETED
            [completedStatus]: [] // COMPLETED is final state
        };

        return validTransitions[currentStatus]?.includes(targetStatus) || false;
    }

    private async updateAssessmentStatus() {
        const classInst = this;

        try {
            await classInst.userAssessment.updateAssessmentStatus(classInst.targetStatusCode, classInst.assessmentId);
            
            Logger.debug(`UpdateAssessmentStatus-updateAssessmentStatus: Successfully updated assessment ${classInst.assessmentId} to ${classInst.targetStatusName} status (${classInst.targetStatusCode})`);
        } catch (error) {
            Logger.error(`UpdateAssessmentStatus-updateAssessmentStatus: Error updating assessment status. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private getStatusDescription(status: string): string {
        switch (status.toLowerCase()) {
            case "started":
            case "start":
                return "started";
            case "completed":
            case "complete":
                return "completed";
            default:
                return status;
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            message: `Assessment status updated to ${classInst.getStatusDescription(classInst.requestedStatus)} successfully`,
            assessment_id: classInst.assessmentId,
            status: classInst.targetStatusName,
            status_code: classInst.targetStatusCode,
            requested_status: classInst.requestedStatus
        });
    }
}
