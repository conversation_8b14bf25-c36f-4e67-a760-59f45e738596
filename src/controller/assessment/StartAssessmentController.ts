import BaseController from "../BaseController";
import { Error, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface RequestBody {
    assessment_id: number;
    customParamsInReq: any;
}

export default class StartAssessmentController extends BaseController {
    private assessmentId: number;
    private userId: number;
    private userAssessment: any;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`StartAssessmentController-constructor: Param body: ${JSON.stringify(params)}`);
        
        classInst.assessmentId = params.assessment_id;
        classInst.userId = params.customParamsInReq?.currentUser?.id;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.validateAssessmentOwnership();
        await classInst.updateAssessmentStatus();

        return classInst.prepareResponse();
    }

    private async validateParams() {
        const classInst = this;

        // Validate assessment_id
        if (!classInst.assessmentId || typeof classInst.assessmentId !== 'number') {
            return classInst.unauthorizedResponse("invalidAssessmentId", "400");
        }

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }
    }

    private async validateAssessmentOwnership() {
        const classInst = this;
        const userAssessmentModel = (await PostgresModel.getDbModels()).userAssessment;

        try {
            // Find the assessment by ID and verify it belongs to the current user
            const assessment = await userAssessmentModel.findByPk(classInst.assessmentId);
            
            if (!assessment) {
                Logger.error(`StartAssessmentController-validateAssessmentOwnership: Assessment not found for id ${classInst.assessmentId}`);
                return classInst.unauthorizedResponse("assessmentNotFound", "404");
            }

            const assessmentData = assessment.get({ plain: true });
            
            if (assessmentData.userId !== classInst.userId) {
                Logger.error(`StartAssessmentController-validateAssessmentOwnership: Assessment ${classInst.assessmentId} does not belong to user ${classInst.userId}`);
                return classInst.unauthorizedResponse("assessmentNotAuthorized", "403");
            }

            // Check if assessment is in a valid state to be started
            const currentStatus = assessmentData.status;
            const createdStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated];
            const startedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];

            if (currentStatus !== createdStatus && currentStatus !== startedStatus) {
                Logger.error(`StartAssessmentController-validateAssessmentOwnership: Assessment ${classInst.assessmentId} is in invalid state ${currentStatus} to be started`);
                return classInst.unauthorizedResponse("invalidAssessmentState", "400");
            }

            classInst.userAssessment = assessmentData;
            Logger.debug(`StartAssessmentController-validateAssessmentOwnership: Assessment validation successful for id ${classInst.assessmentId}`);
        } catch (error) {
            Logger.error(`StartAssessmentController-validateAssessmentOwnership: Error validating assessment. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("validationFailed", "500");
        }
    }

    private async updateAssessmentStatus() {
        const classInst = this;
        const userAssessmentModel = (await PostgresModel.getDbModels()).userAssessment;

        try {
            const startedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];
            
            await userAssessmentModel.updateAssessmentStatus(startedStatus, classInst.assessmentId);
            
            Logger.debug(`StartAssessmentController-updateAssessmentStatus: Assessment ${classInst.assessmentId} status updated to STARTED (${startedStatus})`);
        } catch (error) {
            Logger.error(`StartAssessmentController-updateAssessmentStatus: Error updating assessment status. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        const startedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted];
        
        return ResponseHelper.success({
            message: "Assessment started successfully",
            assessment_id: classInst.assessmentId,
            status: startedStatus,
            status_description: AssessmentConstants.assessmentStatusStarted,
            user_id: classInst.userId,
            assessment_type: classInst.userAssessment.type,
            started_at: new Date().toISOString()
        });
    }
}
