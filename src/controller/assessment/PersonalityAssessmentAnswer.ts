import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import { PersonalityAssessmentSetModelAttributes } from "../../lib/types/Model";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface AssessmentAnswer {
    question_assessment_set_id: number;
    user_answer: number;
}

export default class PersonalityAssessmentAnswer extends BaseController {
    private answers: AssessmentAnswer[];
    private userId: number;
    private personalityAssessmentSet: any;

    constructor(params: any) {
        super(params);
        const classInst = this;
        Logger.debug(`PersonalityAssessmentAnswer-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`PersonalityAssessmentAnswer-constructor: Param body: ${JSON.stringify(params.customParamsInReq.currentUser)}`)
        classInst.answers = params;
        classInst.userId = params.userId;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.updateAnswers();

        return classInst.prepareResponse();
    }

    private async validateParams() {
        const classInst = this;

        if (!classInst.answers || !Array.isArray(classInst.answers) || classInst.answers.length === 0) {
            return classInst.unauthorizedResponse("invalidAnswers", "400");
        }

        // Validate each answer
        for (const answer of classInst.answers) {
            if (!answer.question_assessment_set_id || !answer.user_answer) {
                return classInst.unauthorizedResponse("invalidAnswerFormat", "400");
            }
        }
    }

    private async updateAnswers() {
        const classInst = this;
        const personalityAssessmentSet = (await PostgresModel.getDbModels()).personalityAssessmentSet;

        try {
            for (const answer of classInst.answers) {
                // Get the assessment set entry
                const assessmentSets = await personalityAssessmentSet.findByAssessmentId(answer.question_assessment_set_id);
                const assessmentSet = assessmentSets.find(qs => qs.id === answer.question_assessment_set_id);

                if (!assessmentSet) {
                    Logger.error(`PersonalityAssessmentAnswer-updateAnswers: Assessment set not found for id ${answer.question_assessment_set_id}`);
                    return classInst.unauthorizedResponse("assessmentSetNotFound", "404");
                }

                // Update the user answer
                await personalityAssessmentSet.update(
                    { userAnswer: answer.user_answer },
                    { id: answer.question_assessment_set_id }
                );

                Logger.debug(`PersonalityAssessmentAnswer-updateAnswers: Updated answer for assessment set ${answer.question_assessment_set_id} with answer ${answer.user_answer}`);
            }
        } catch (error) {
            Logger.error(`PersonalityAssessmentAnswer-updateAnswers: Error updating answers. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            message: "All answers updated successfully",
            updatedAnswers: classInst.answers.length
        });
    }
}
