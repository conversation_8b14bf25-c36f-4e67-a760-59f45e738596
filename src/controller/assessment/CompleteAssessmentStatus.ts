import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import { UserAssessmentModelAttributes } from "../../lib/types/Model";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface RequestBody {
    assessment_id: number;
    customParamsInReq: any;
}

export default class CompleteAssessmentStatus extends BaseController {
    private assessmentId: number;
    private userId: number;
    private userAssessment: any;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`CompleteAssessmentStatus-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`CompleteAssessmentStatus-constructor: User details: ${JSON.stringify(params.customParamsInReq?.currentUser)}`)
        
        classInst.assessmentId = params.assessment_id;
        classInst.userId = params.customParamsInReq?.currentUser?.id;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.initializeModels();
        await classInst.validateParams();
        await classInst.validateAssessmentOwnership();
        await classInst.updateAssessmentStatus();

        return classInst.prepareResponse();
    }

    private async initializeModels() {
        const classInst = this;
        const postgresModel = await PostgresModel.getDbModels();
        classInst.userAssessment = postgresModel.userAssessment;
    }

    private async validateParams() {
        const classInst = this;

        // Validate assessment_id
        if (!classInst.assessmentId || typeof classInst.assessmentId !== 'number') {
            return classInst.unauthorizedResponse("invalidAssessmentId", "400");
        }

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }
    }

    private async validateAssessmentOwnership() {
        const classInst = this;

        try {
            // Find the assessment by ID
            const assessment = await classInst.userAssessment.findById(classInst.assessmentId);
            
            if (!assessment) {
                Logger.error(`CompleteAssessmentStatus-validateAssessmentOwnership: Assessment not found for id ${classInst.assessmentId}`);
                return classInst.unauthorizedResponse("assessmentNotFound", "404");
            }

            // Check if the assessment belongs to the authenticated user
            if (assessment.userId !== classInst.userId) {
                Logger.error(`CompleteAssessmentStatus-validateAssessmentOwnership: Assessment ${classInst.assessmentId} does not belong to user ${classInst.userId}`);
                return classInst.unauthorizedResponse("assessmentNotAuthorized", "403");
            }

            // Check if assessment is in a valid state to be completed
            const validStatuses = [
                AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted],
                AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
            ];

            if (!validStatuses.includes(assessment.status)) {
                Logger.error(`CompleteAssessmentStatus-validateAssessmentOwnership: Assessment ${classInst.assessmentId} is not in a valid state to be completed. Current status: ${assessment.status}`);
                return classInst.unauthorizedResponse("invalidAssessmentStatus", "400");
            }

        } catch (error) {
            Logger.error(`CompleteAssessmentStatus-validateAssessmentOwnership: Error validating assessment ownership. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("validationFailed", "500");
        }
    }

    private async updateAssessmentStatus() {
        const classInst = this;

        try {
            const completedStatus = AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted];
            
            await classInst.userAssessment.updateAssessmentStatus(completedStatus, classInst.assessmentId);
            
            Logger.debug(`CompleteAssessmentStatus-updateAssessmentStatus: Successfully updated assessment ${classInst.assessmentId} to completed status (${completedStatus})`);
        } catch (error) {
            Logger.error(`CompleteAssessmentStatus-updateAssessmentStatus: Error updating assessment status. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            message: "Assessment status updated to completed successfully",
            assessment_id: classInst.assessmentId,
            status: AssessmentConstants.assessmentStatusCompleted,
            status_code: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCompleted]
        });
    }
}
