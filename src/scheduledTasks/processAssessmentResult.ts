import AssessmentResultProcessor from '../scheduledProcessor/assessmentResults';
import Logger from '../lib/Logger';


async function runProcessor() {
    try {
        const processor = new AssessmentResultProcessor();
        await processor.initializeModels();
        await processor.processCompletedAssessmentzes();
    } catch (error) {
        Logger.error(`Error in runProcessor: ${error}`);
    }
}

// Start the processor
Logger.info('Starting assessment result processor...');
runProcessor();
