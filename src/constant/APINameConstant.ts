/**
 * Module to define API name.
 */
class ApiNameConstant {
    get userLogin(): string {
        return 'userLogin';
    }

    get userSignup(): string {
        return 'userSignup';
    }

    get refreshToken(): string {
        return 'refreshToken';
    }

    get logout(): string {
        return 'logout';
    }

    public get startPersonalityAssessment(): string {
        return "startAssessment";
    }

    public get startOccupationAssessment(): string {
        return "startOccupationAssessment";
    }

    public get savePersonalityAssessmentAnswer(): string {
        return "saveAssessmentResponse";
    }

    public get getOccupationsByPersonality(): string {
        return "getOccupationsByPersonality";
    }

    public get saveOccupationChoice(): string {
        return "saveOccupationChoice";
    }

    public get updateUserProfile(): string {
        return "updateUserProfile";
    }

    public get signupWithLink(): string {
        return "signupWithLink";
    }

    public get verifyOtp(): string {
        return "verifyOtp";
    }

    public get resendOtp(): string {
        return "resendOtp";
    }

    public get updateAssessmentStatus(): string {
        return "updateAssessmentStatus";
    }
}

export default new ApiNameConstant();
