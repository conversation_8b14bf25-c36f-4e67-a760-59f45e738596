{"info": {"_postman_id": "cb609996-8e42-41c0-ab2b-866a81f43eb0", "name": "Backend API", "description": "Complete API collection for ST Backend with Authentication, OTP verification, User Management, and Assessment APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13919853"}, "item": [{"name": "Authentication & User Management", "item": [{"name": "Check Email Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Email check successful\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    console.log(`${JSON.stringify(jsonData)}`)", "    if (jsonData.success) {", "        var responseData = jsonData.data", "        console.log(`${JSON.stringify(responseData)}`)", "        if (responseData.temp_token) {", "            pm.environment.set(\"temp_token\", responseData.temp_token);", "            console.log(\"Temp token saved:\", responseData.temp_token);", "        }", "        if (responseData.otp_code) {", "            pm.environment.set(\"test_otp\", responseData.otp_code);", "            console.log(\"OTP saved:\", responseData.otp_code);", "        }", "        console.log(\"Login phase:\", responseData.login_phase);", "    }", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"referral_code\": \"SKL-01\"\n}"}, "url": {"raw": "{{base_url}}/api/user/signup-with-link", "host": ["{{base_url}}"], "path": ["api", "user", "signup-with-link"]}}, "response": []}, {"name": "Verify OTP", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"OTP verification successful\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    console.log(`${JSON.stringify(jsonData)}`)", "    if (jsonData.data && jsonData.data.verified) {", "        pm.expect(jsonData.data.verified).to.be.true;", "        pm.environment.set(\"access_token\", jsonData.data.accessToken);", "        pm.environment.set(\"refresh_token\", jsonData.data.refreshToken);", "        console.log(\"Email verified successfully\");", "    }", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"otp_code\": \"{{test_otp}}\",\n    \"otp_type\": \"email_verification\"\n}"}, "url": {"raw": "{{base_url}}/api/user/verify-otp", "host": ["{{base_url}}"], "path": ["api", "user", "verify-otp"]}}, "response": []}, {"name": "Resend OTP", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"OTP resent successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data.otp_code) {", "        pm.environment.set(\"test_otp\", jsonData.data.otp_code);", "        console.log(\"New OTP saved:\", jsonData.data.otp_code);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"otp_type\": \"email_verification\"\n}"}, "url": {"raw": "{{base_url}}/api/user/resend-otp", "host": ["{{base_url}}"], "path": ["api", "user", "resend-otp"]}}, "response": []}, {"name": "Update User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Profile updated successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    console.log(\"Profile updated:\", jsonData.data.updatedFields);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{user_name}}\",\n    \"mobile_number\": \"{{user_mobile}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/update-profile", "host": ["{{base_url}}"], "path": ["api", "user", "update-profile"]}}, "response": []}, {"name": "User Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"{{user_name}}\",\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{user_password}}\",\n    \"username\": \"{{username}}\",\n    \"mobileNumber\": \"{{user_mobile}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/signup", "host": ["{{base_url}}"], "path": ["api", "user", "signup"]}}, "response": []}, {"name": "User Login", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Login successful\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data.data.accessToken) {", "        pm.environment.set(\"access_token\", jsonData.data.data.accessToken);", "        pm.environment.set(\"refresh_token\", jsonData.data.data.refreshToken);", "        console.log(\"Access token saved\");", "        console.log(\"Refresh token saved\");", "    }", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/login", "host": ["{{base_url}}"], "path": ["api", "user", "login"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Token refreshed successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data.accessToken) {", "        pm.environment.set(\"access_token\", jsonData.data.accessToken);", "        console.log(\"New access token saved\");", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/refresh-token", "host": ["{{base_url}}"], "path": ["api", "user", "refresh-token"]}}, "response": []}, {"name": "User <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/user/logout", "host": ["{{base_url}}"], "path": ["api", "user", "logout"]}}, "response": []}, {"name": "Get Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/user/get-users", "host": ["{{base_url}}"], "path": ["api", "user", "get-users"]}}, "response": []}]}, {"name": "Expert Management", "item": [{"name": "Get All Experts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/expert/list", "host": ["{{base_url}}"], "path": ["api", "expert", "list"]}}, "response": []}, {"name": "Get Expert by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/expert/{{expert_id}}", "host": ["{{base_url}}"], "path": ["api", "expert", "{{expert_id}}"]}}, "response": []}, {"name": "Create Expert Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Dr. <PERSON>\",\n    \"specialization\": \"Mathematics\",\n    \"experience\": 10,\n    \"bio\": \"Expert in advanced mathematics\",\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/expert/create", "host": ["{{base_url}}"], "path": ["api", "expert", "create"]}}, "response": []}, {"name": "Update Expert Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Dr. <PERSON>\",\n    \"specialization\": \"Advanced Mathematics\",\n    \"experience\": 12\n}"}, "url": {"raw": "{{base_url}}/api/expert/{{expert_id}}", "host": ["{{base_url}}"], "path": ["api", "expert", "{{expert_id}}"]}}, "response": []}]}, {"name": "Assessment APIs", "item": [{"name": "Start Personality Assessment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Personality assessment started successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    if (jsonData.data && jsonData.data.assessment_id) {", "        pm.environment.set(\"assessment_id\", jsonData.data.assessment_id);", "        console.log(\"Assessment ID saved:\", jsonData.data.assessment_id);", "    }", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/assessment/start-personality-assessment", "host": ["{{base_url}}"], "path": ["api", "assessment", "start-personality-assessment"]}}, "response": []}, {"name": "Save Personality Assessment Answer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Answer saved successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"assessment_id\": \"{{assessment_id}}\",\n    \"answers\": [\n        {\n            \"question_assessment_set_id\": 1,\n            \"user_answer\": 1\n        },\n        {\n            \"question_assessment_set_id\": 2,\n            \"user_answer\": -1\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/assessment/save-assessment-response", "host": ["{{base_url}}"], "path": ["api", "assessment", "save-assessment-response"]}}, "response": []}, {"name": "Start Assessment Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Assessment status updated to started successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"assessment_id\": {{assessment_id}}\n}"}, "url": {"raw": "{{base_url}}/api/assessment/start-assessment-status", "host": ["{{base_url}}"], "path": ["api", "assessment", "start-assessment-status"]}}, "response": []}, {"name": "Complete Assessment Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Assessment status updated to completed successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"assessment_id\": {{assessment_id}}\n}"}, "url": {"raw": "{{base_url}}/api/assessment/complete-assessment-status", "host": ["{{base_url}}"], "path": ["api", "assessment", "complete-assessment-status"]}}, "response": []}, {"name": "Get Assessment Users (Test Endpoint)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/assessment/get-users", "host": ["{{base_url}}"], "path": ["api", "assessment", "get-users"]}}, "response": []}]}, {"name": "Authentication Flow Tests", "item": [{"name": "New User Registration Flow", "item": [{"name": "1. <PERSON> (New User)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"New user email check\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.email_exists).to.be.false;", "    pm.expect(jsonData.data.user_created).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/user/check-email-status", "host": ["{{base_url}}"], "path": ["api", "user", "check-email-status"]}}, "response": []}, {"name": "2. <PERSON>erify <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"otp_code\": \"123456\",\n    \"otp_type\": \"email_verification\"\n}"}, "url": {"raw": "{{base_url}}/api/user/verify-otp", "host": ["{{base_url}}"], "path": ["api", "user", "verify-otp"]}}, "response": []}, {"name": "3. Set Password & Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{temp_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"password\": \"newpassword123\",\n    \"name\": \"New User\",\n    \"mobile_number\": \"**********\"\n}"}, "url": {"raw": "{{base_url}}/api/user/update-profile", "host": ["{{base_url}}"], "path": ["api", "user", "update-profile"]}}, "response": []}, {"name": "4. <PERSON><PERSON> with New Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/user/login", "host": ["{{base_url}}"], "path": ["api", "user", "login"]}}, "response": []}]}, {"name": "Existing User Login Flow", "item": [{"name": "1. <PERSON> (Existing)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Existing user email check\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.email_exists).to.be.true;", "    console.log(\"Login phase:\", jsonData.data.login_phase);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/check-email-status", "host": ["{{base_url}}"], "path": ["api", "user", "check-email-status"]}}, "response": []}, {"name": "2. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/user/login", "host": ["{{base_url}}"], "path": ["api", "user", "login"]}}, "response": []}]}]}, {"name": "Deprecated", "item": [{"name": "Start Occupation Assessment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Occupation assessment started successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/assessment/start-occupation-assessment", "host": ["{{base_url}}"], "path": ["api", "assessment", "start-occupation-assessment"]}}, "response": []}, {"name": "Get Occupations by Personality", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Occupations retrieved successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/assessment/occupations", "host": ["{{base_url}}"], "path": ["api", "assessment", "occupations"]}}, "response": []}, {"name": "Save Occupation Choice", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Occupation choice saved successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"occupation_id\": 1,\n    \"choice_reason\": \"Interested in this field\"\n}"}, "url": {"raw": "{{base_url}}/api/assessment/save-occupation-choice", "host": ["{{base_url}}"], "path": ["api", "assessment", "save-occupation-choice"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:4023", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "temp_token", "value": "", "type": "string"}, {"key": "test_email", "value": "<EMAIL>", "type": "string"}, {"key": "test_otp", "value": "", "type": "string"}, {"key": "user_name", "value": "Test User", "type": "string"}, {"key": "user_password", "value": "password123", "type": "string"}, {"key": "user_mobile", "value": "1234567890", "type": "string"}, {"key": "username", "value": "testuser", "type": "string"}, {"key": "assessment_id", "value": "1", "type": "string"}, {"key": "expert_id", "value": "1", "type": "string"}]}